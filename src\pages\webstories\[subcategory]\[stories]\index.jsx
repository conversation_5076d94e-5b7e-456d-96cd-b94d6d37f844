import React from "react";
import { getWebStories } from "@/pages/api/WebStoriesApi";
import Head from "next/head";

import { dateFormateWithTimeShort } from "@/utils/Util";
import SeoHeader from "@/components/seo/SeoHeader";
import BreadcrumbSchema from "@/components/seo/BreadcrumbSchema";
import ImageGallerySchema from "@/components/seo/ImageGallerySchema";
import MediaGallerySchema from "@/components/seo/MediaGallerySchema";
import { Const } from "@/utils/Constants";

export const config = { amp: true };

const WebStoryDetail = ({ data, nextData, breadcrumbs, meta, pathname }) => {
	// AMP-compliant CSS without i-amphtml- prefixes
	const ampStoryCSS = `
		.brand-logo {
			position: absolute;
			top: 0px;
			left: 20px;
			color: white;
			text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
			z-index: 10;
		}

		.story-content {
			position: absolute;
			bottom: 0;
			left: 0;
			right: 0;
			padding: 20px;
			background: linear-gradient(to bottom, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0) 34%, rgba(0, 0, 0, 1) 100%);
		}

		.story-text {
			color: white;
			text-align: left;
			font-family: NeueHaasDisplayLight, sans-serif;
		}

		.story-text h1 {
			font-size: 24px;
			font-family: NeueHaasDisplayLight, sans-serif;
			margin-bottom: 10px;
			font-weight: 400;
			line-height: 1.1;
		}

		.story-text div {
			font-family: NeueHaasDisplayLight, sans-serif;
			font-size: 16px;
			line-height: 1.3;
			font-weight: 300;
		}

		.story-text p {
			font-family: NeueHaasDisplayLight, sans-serif;
			font-size: 16px;
			line-height: 1.3;
			font-weight: 300;
		}

		.story-text p a {
			color: #fff;
		}

		.story-text small {
			display: block;
			margin-top: 8px;
			opacity: 0.8;
			font-family: NeueHaasDisplayLight, sans-serif;
			font-size: 12px;
			font-weight: 300;
		}

		[template=vertical] {
			align-content: end;
		}

		.next-story-preview {
			color: #fff;
			padding-bottom: 2rem;
		}

		.next-story-btn {
			background: rgba(255, 255, 255, 0.9);
			color: #000;
			padding: 12px 24px;
			border-radius: 25px;
			text-decoration: none;
			font-weight: bold;
			display: inline-block;
		}
	`;

	return (
		<>
			<Head>
				<meta name="amp-to-amp-navigation" content="AMP-Redirect" />
				<style amp-custom dangerouslySetInnerHTML={{ __html: ampStoryCSS }} />
				<script
					async
					custom-element="amp-story"
					src="https://cdn.ampproject.org/v0/amp-story-1.0.js"
				></script>
				<script
					async
					custom-element="amp-bind"
					src="https://cdn.ampproject.org/v0/amp-bind-0.1.js"
				></script>
				<script
					async
					custom-element="amp-ad"
					src="https://cdn.ampproject.org/v0/amp-ad-0.1.js"
				></script>
				<SeoHeader meta={meta} pathname={pathname} />
				<BreadcrumbSchema itemList={breadcrumbs} />
				<ImageGallerySchema
					title={data?.slides?.[0]?.title || ""}
					description={data?.slides?.[0]?.description || ""}
					url={Const.ClientLink + pathname}
					datePublished={data?.timestamp || ""}
					data={data?.slides || []}
				/>
				<MediaGallerySchema
					title={data?.slides?.[0]?.title || ""}
					description={data?.slides?.[0]?.description || ""}
					data={data?.slides || []}
				/>
			</Head>

			<amp-story
				standalone
				title={data?.title || ""}
				publisher="Manifest"
				publisher-logo-src="/logo.png"
				poster-portrait-src={data?.coverImg || ""}
			>
				{data?.slides?.map((slide, index) => {
					const shouldShowAd =
						data.slides.length >= 5 && (index + 1) % 3 === 0 && index !== data.slides.length - 1;

					return (
						<React.Fragment key={index}>
							<amp-story-page id={`page-${index}`}>
								<amp-story-grid-layer template="fill">
									<amp-img
										src={slide?.image || ""}
										width="720"
										height="1280"
										layout="fill"
										alt={slide?.altName || ""}
									></amp-img>
								</amp-story-grid-layer>

								{index === 0 && (
									<amp-story-grid-layer template="vertical">
										<div className="brand-logo">
											<amp-img
												src="/logo svg.svg"
												width="200"
												height="80"
												layout="fixed"
												alt="Manifest Logo"
											></amp-img>
										</div>
									</amp-story-grid-layer>
								)}

								<amp-story-grid-layer template="vertical" className="story-content">
									<div className="story-text">
										<h1>{slide?.title || ""}</h1>
										{slide?.description && (
											<div dangerouslySetInnerHTML={{ __html: slide.description }} />
										)}
										{slide?.contributor?.length > 0 && (
											<small>Photo Credit: {slide.contributor.join(", ")}</small>
										)}
										{index === 0 && slide?.timestamp && (
											<small>Published: {dateFormateWithTimeShort(slide.timestamp)}</small>
										)}
									</div>
								</amp-story-grid-layer>
							</amp-story-page>

							{shouldShowAd && (
								<amp-story-page id={`ad-page-${index}`} auto-advance-after="7s">
									<amp-story-grid-layer template="vertical">
										<amp-ad
											width="320"
											height="480"
											type="doubleclick"
											data-slot="/23290324739/Manifest-AMP-Stories"
											data-multi-size="300x250,320x50"
											layout="responsive"
											json='{"targeting":{"section":[],"sub-section":[]}}'
										>
											<div placeholder>Loading ad...</div>
											<div fallback>Ad failed to load</div>
										</amp-ad>
									</amp-story-grid-layer>
								</amp-story-page>
							)}
						</React.Fragment>
					);
				})}

				{/* Next Story Preview Page */}
				{nextData?.slug && nextData?.coverImg && (
					<amp-story-page id="next-story-preview">
						<amp-story-grid-layer template="fill">
							<amp-img
								src={nextData.coverImg}
								width="720"
								height="1280"
								layout="fill"
								alt={nextData.altName || "Next Story"}
							></amp-img>
						</amp-story-grid-layer>

						<amp-story-grid-layer template="vertical" className="next-story-overlay story-content">
							<div className="next-story-preview">
								<div className="preview-label">Next Story</div>
								<h2>{nextData.title}</h2>
							</div>
						</amp-story-grid-layer>

						<amp-story-cta-layer>
							<a href={nextData.slug} className="next-story-btn">
								Read Next Story
							</a>
						</amp-story-cta-layer>
					</amp-story-page>
				)}
			</amp-story>
		</>
	);
};

export default WebStoryDetail;
WebStoryDetail.config = { amp: true };

export async function getServerSideProps(context) {
	const { stories } = context.params;
	const url = `/${stories}`;
	try {
		const storiesRes = await getWebStories(url);

		if (!storiesRes || Object.keys(storiesRes.data).length === 0) {
			return {
				notFound: true,
			};
		}

		const storyData = storiesRes.data.current.data;
		const newObject = {
			title: storyData.title,
			description: "",
			image: storyData.coverImg,
			altName: storyData.altName,
			sequence: -1,
			contributor: [],
			timestamp: storyData.timestamp,
		};

		if (Array.isArray(storyData.slides)) {
			storyData.slides.unshift(newObject);
		}

		return {
			props: {
				data: storyData ?? {},
				previousData: storiesRes.data.previous ?? {},
				nextData: storiesRes.data.next ?? {},
				breadcrumbs: storiesRes.data.current.breadcrumbs ?? [],
				tag: storiesRes.data.current.tag ?? [],
				meta: storiesRes.data.current.meta ?? {},
				pathname: context.resolvedUrl || context.req.url || "",
			},
		};
	} catch (error) {
		console.error("Error fetching data:", error.message);
		return {
			notFound: true,
		};
	}
}
