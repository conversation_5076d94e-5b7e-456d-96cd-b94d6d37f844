import AMPNavbar from "@/components/amp/AMPNavbar";
import { ampNavbarCSS } from "@/components/amp/ampCss";
import Head from "next/head";

export const config = { amp: true };

const AMPWebstoryPage = () => {
	return (
		<>
			<Head>
				<title>AMP Web Stories - Manifest</title>
				<meta name="description" content="Browse AMP Web Stories on Manifest" />
				<style amp-custom dangerouslySetInnerHTML={{ __html: ampNavbarCSS }} />
			</Head>
			<div>
				<AMPNavbar />
				<h1>AMP Webstory Page</h1>
			</div>
		</>
	);
};

export default AMPWebstoryPage;
